services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: harmoni360-db
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-Harmoni360}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-StrongProductionPassword123!}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/sql/init:/docker-entrypoint-initdb.d
    networks:
      - harmoni360
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: harmoni360-cache
    command: redis-server --requirepass ${REDIS_PASSWORD:-RedisProductionPassword123!}
    networks:
      - harmoni360
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Harmoni360 Web Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    image: harmoni360:latest
    container_name: harmoni360-app
    ports:
      - "${APP_PORT:-8080}:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=Host=postgres;Port=5432;Database=${POSTGRES_DB:-Harmoni360};Username=${POSTGRES_USER:-postgres};Password=${POSTGRES_PASSWORD:-StrongProductionPassword123!}
      - ConnectionStrings__Redis=redis:6379,password=${REDIS_PASSWORD:-RedisProductionPassword123!}
      - Jwt__Key=${JWT_KEY:-YourSuperSecretProductionJwtKeyThatMustBeAtLeast32CharactersLong!}
      - Jwt__Issuer=${JWT_ISSUER:-Harmoni360}
      - Jwt__Audience=${JWT_AUDIENCE:-Harmoni360Users}
      - Jwt__ExpirationMinutes=${JWT_EXPIRATION_MINUTES:-60}
      - Jwt__RefreshTokenExpirationDays=${JWT_REFRESH_EXPIRATION_DAYS:-7}
      - Serilog__MinimumLevel__Default=Information
      - Serilog__MinimumLevel__Override__Microsoft=Warning
      - Serilog__MinimumLevel__Override__System=Warning
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - harmoni360
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx Reverse Proxy (optional but recommended for production)
  nginx:
    image: nginx:alpine
    container_name: harmoni360-nginx
    ports:
      - "${NGINX_PORT:-80}:80"
      - "${NGINX_HTTPS_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - app
    networks:
      - harmoni360
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  app_uploads:
    driver: local
  app_logs:
    driver: local
  nginx_logs:
    driver: local

networks:
  harmoni360:
    driver: bridge