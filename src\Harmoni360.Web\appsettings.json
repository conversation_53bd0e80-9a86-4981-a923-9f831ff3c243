{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=Harmoni360_Dev;Username=********;Password=********", "Redis": "localhost:6379"}, "Jwt": {"Key": "YourSecretKeyHereWhichShouldBeAtLeast32CharactersLongForSecurity", "Issuer": "Harmoni360", "Audience": "Harmoni360Users", "ExpirationMinutes": "60", "RefreshTokenExpirationDays": "7"}, "Seq": {"ServerUrl": "http://localhost:5341"}, "Application": {"DemoMode": true, "Environment": "Development", "DemoSettings": {"ShowDemoBanner": true, "AllowDataModification": true, "AllowUserCreation": true, "AllowDataDeletion": true, "ShowSampleDataLabels": true, "AutoResetData": true, "AutoResetIntervalHours": 24, "BannerMessage": "🎯 Demo Mode - Explore with sample data. Some features are limited for demonstration purposes.", "RestrictedOperations": ["DeleteAllData", "ResetDatabase", "ModifyConfiguration", "SendEmails", "ManageUsers"]}}, "DataSeeding": {"ForceReseed": false, "Categories": {"Essential": true, "SampleData": false, "UserAccounts": false}}}