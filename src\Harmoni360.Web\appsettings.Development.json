{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=Harmoni360_Dev;Username=postgres;Password=***********"}, "Jwt": {"Key": "YourSuperSecretDevelopmentJwtKeyThatIsAtLeast32CharactersLong!", "Issuer": "Harmoni360", "Audience": "Harmoni360Users", "ExpirationMinutes": 1440, "RefreshTokenExpirationDays": 30}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information", "Harmoni360.Infrastructure.Services.DataSeeders": "Debug"}}, "AllowedHosts": "*", "Cors": {"AllowedOrigins": "http://localhost:5173,http://localhost:3000"}, "Kestrel": {"Endpoints": {"Http": {"Url": "http://localhost:5000"}}}, "UseSpaProxy": false, "Application": {"DemoMode": true, "Environment": "Development", "DemoSettings": {"ShowDemoBanner": true, "AllowDataModification": true, "AllowUserCreation": true, "AllowDataDeletion": true, "ShowSampleDataLabels": true, "BannerMessage": "🎯 Demo Mode - Explore with sample data. Some features are limited for demonstration purposes."}}, "DataSeeding": {"ForceReseed": true, "Categories": {"Essential": true, "SampleData": true, "UserAccounts": true}}}