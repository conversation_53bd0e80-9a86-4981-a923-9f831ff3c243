# Harmoni360 Production Docker Compose Configuration
# Optimized for standalone server deployment with 100+ concurrent users

version: '3.8'

services:
  # PostgreSQL Database with Production Configuration
  postgres:
    image: postgres:15-alpine
    container_name: harmoni360-db-prod
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-Harmoni360_Prod}
      POSTGRES_USER: ${POSTGRES_USER:-harmoni360}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=en_US.UTF-8"
      # Performance tuning for production
      POSTGRES_SHARED_BUFFERS: ${POSTGRES_SHARED_BUFFERS:-2GB}
      POSTGRES_EFFECTIVE_CACHE_SIZE: ${POSTGRES_EFFECTIVE_CACHE_SIZE:-6GB}
      POSTGRES_WORK_MEM: ${POSTGRES_WORK_MEM:-64MB}
      POSTGRES_MAINTENANCE_WORK_MEM: ${POSTGRES_MAINTENANCE_WORK_MEM:-512MB}
      POSTGRES_MAX_CONNECTIONS: ${POSTGRES_MAX_CONNECTIONS:-300}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - postgres_backups:/backups
      - ./scripts/sql/init:/docker-entrypoint-initdb.d:ro
      - ./config/postgres/postgresql.conf:/etc/postgresql/postgresql.conf:ro
      - ./config/postgres/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
    command: >
      postgres
      -c config_file=/etc/postgresql/postgresql.conf
      -c hba_file=/etc/postgresql/pg_hba.conf
      -c log_statement=mod
      -c log_min_duration_statement=1000
      -c log_checkpoints=on
      -c log_connections=on
      -c log_disconnections=on
    networks:
      - harmoni360-backend
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-harmoni360} -d ${POSTGRES_DB:-Harmoni360_Prod}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 8G
          cpus: '4.0'
        reservations:
          memory: 4G
          cpus: '2.0'
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

  # Redis Cache with Persistence
  redis:
    image: redis:7-alpine
    container_name: harmoni360-cache-prod
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD}
      --appendonly yes
      --appendfsync everysec
      --save 900 1
      --save 300 10
      --save 60 10000
      --maxmemory 2gb
      --maxmemory-policy allkeys-lru
      --tcp-keepalive 60
      --timeout 300
    volumes:
      - redis_data:/data
      - ./config/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - harmoni360-backend
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

  # Harmoni360 Web Application
  app:
    build:
      context: .
      dockerfile: Dockerfile.flyio
      args:
        BUILDKIT_INLINE_CACHE: 1
    image: harmoni360:prod-${BUILD_VERSION:-latest}
    container_name: harmoni360-app-prod
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - ASPNETCORE_FORWARDEDHEADERS_ENABLED=true
      # Database Configuration
      - ConnectionStrings__DefaultConnection=Host=postgres;Port=5432;Database=${POSTGRES_DB:-Harmoni360_Prod};Username=${POSTGRES_USER:-harmoni360};Password=${POSTGRES_PASSWORD};Pooling=true;MinPoolSize=10;MaxPoolSize=100;CommandTimeout=30;
      - ConnectionStrings__Redis=redis:6379,password=${REDIS_PASSWORD},connectTimeout=5000,syncTimeout=5000,responseTimeout=5000
      # JWT Configuration
      - Jwt__Key=${JWT_KEY}
      - Jwt__Issuer=${JWT_ISSUER:-Harmoni360}
      - Jwt__Audience=${JWT_AUDIENCE:-Harmoni360Users}
      - Jwt__ExpirationMinutes=${JWT_EXPIRATION_MINUTES:-60}
      - Jwt__RefreshTokenExpirationDays=${JWT_REFRESH_EXPIRATION_DAYS:-7}
      # Logging Configuration
      - Seq__ServerUrl=http://seq:5341
      - Serilog__MinimumLevel__Default=Information
      - Serilog__MinimumLevel__Override__Microsoft=Warning
      - Serilog__MinimumLevel__Override__System=Warning
      # File Upload Configuration
      - FileStorage__UploadsPath=/app/uploads
      - FileStorage__MaxFileSize=${MAX_FILE_SIZE:-104857600}
      # Performance Configuration
      - DOTNET_gcServer=1
      - DOTNET_gcConcurrent=1
      - DOTNET_GCHeapCount=4
      - DOTNET_ThreadPool_UnfairSemaphoreSpinLimit=6
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      seq:
        condition: service_healthy
    networks:
      - harmoni360-backend
      - harmoni360-frontend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '4.0'
        reservations:
          memory: 1G
          cpus: '1.0'
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

  # Nginx Reverse Proxy with SSL Termination
  nginx:
    image: nginx:alpine
    container_name: harmoni360-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/nginx/conf.d:/etc/nginx/conf.d:ro
      - ./config/nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
      - app_uploads:/var/www/uploads:ro
    depends_on:
      app:
        condition: service_healthy
    networks:
      - harmoni360-frontend
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 128M
          cpus: '0.25'
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

  # Seq Logging Service
  seq:
    image: datalust/seq:latest
    container_name: harmoni360-seq-prod
    environment:
      - ACCEPT_EULA=Y
      - SEQ_FIRSTRUN_ADMINPASSWORDHASH=${SEQ_ADMIN_PASSWORD_HASH}
    volumes:
      - seq_data:/data
    networks:
      - harmoni360-backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5341/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.25'
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: harmoni360-prometheus-prod
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    volumes:
      - ./config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - harmoni360-backend
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: harmoni360-grafana-prod
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning:ro
    depends_on:
      - prometheus
    networks:
      - harmoni360-backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-/opt/harmoni360}/postgres
  postgres_backups:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${BACKUP_PATH:-/opt/harmoni360/backups}/postgres
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-/opt/harmoni360}/redis
  app_uploads:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-/opt/harmoni360}/uploads
  app_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${LOG_PATH:-/opt/harmoni360/logs}/app
  nginx_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${LOG_PATH:-/opt/harmoni360/logs}/nginx
  seq_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-/opt/harmoni360}/seq
  prometheus_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-/opt/harmoni360}/prometheus
  grafana_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-/opt/harmoni360}/grafana

networks:
  harmoni360-backend:
    driver: bridge
    internal: true
    ipam:
      config:
        - subnet: **********/24
  harmoni360-frontend:
    driver: bridge
    ipam:
      config:
        - subnet: **********/24
