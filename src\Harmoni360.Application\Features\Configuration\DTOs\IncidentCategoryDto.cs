namespace Harmoni360.Application.Features.Configuration.DTOs;

public class IncidentCategoryDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Color { get; set; } = "#007bff";
    public string Icon { get; set; } = "fa-exclamation-triangle";
    public bool IsActive { get; set; }
    public int DisplayOrder { get; set; }
    public bool RequiresImmediateAction { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? LastModifiedAt { get; set; }
    public string? LastModifiedBy { get; set; }
}