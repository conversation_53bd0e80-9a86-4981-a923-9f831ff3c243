// Dashboard Responsive Styles

// Breakpoints
$mobile: 576px;
$tablet: 768px;
$desktop: 992px;
$wide: 1200px;

// Dashboard Container
.dashboard-container {
  padding: 1rem;
  
  @media (max-width: $mobile) {
    padding: 0.5rem;
  }
}

// Filter Controls
.dashboard-filters {
  .filter-controls {
    @media (max-width: $tablet) {
      flex-direction: column;
      gap: 1rem;
      
      .btn-group {
        width: 100%;
        flex-wrap: wrap;
        
        .btn {
          flex: 1 1 auto;
          min-width: 80px;
        }
      }
      
      .form-select {
        width: 100%;
      }
    }
  }
  
  .refresh-controls {
    @media (max-width: $tablet) {
      width: 100%;
      justify-content: space-between !important;
      margin-top: 1rem;
      
      .btn-toolbar {
        flex: 1;
      }
      
      .text-medium-emphasis {
        text-align: right;
        font-size: 0.75rem;
      }
    }
  }
}

// Stats Cards
.stats-card {
  @media (max-width: $mobile) {
    .card-body {
      padding: 1rem;
      
      .stats-value {
        font-size: 1.5rem;
      }
      
      .stats-title {
        font-size: 0.875rem;
      }
      
      .stats-icon {
        font-size: 2rem;
      }
    }
  }
}

// Chart Cards
.chart-card {
  @media (max-width: $tablet) {
    .card-body {
      padding: 0.75rem;
    }
    
    // Adjust chart heights for mobile
    .donut-chart-container,
    .bar-chart-container,
    .line-chart-container {
      max-height: 250px;
      overflow: auto;
    }
  }
}

// Progress Cards
.progress-card {
  @media (max-width: $mobile) {
    .card-body {
      padding: 0.75rem;
      
      .progress-value {
        font-size: 1.25rem;
      }
      
      .progress-title {
        font-size: 0.875rem;
      }
    }
  }
}

// Recent Items List
.recent-items-list {
  @media (max-width: $tablet) {
    .list-group-item {
      padding: 0.75rem;
      
      .item-title {
        font-size: 0.9rem;
      }
      
      .item-subtitle {
        font-size: 0.8rem;
      }
      
      .badge {
        font-size: 0.7rem;
      }
    }
  }
}

// Department Stats
.department-stats {
  @media (max-width: $tablet) {
    .department-item {
      padding: 0.75rem !important;
      margin-bottom: 0.5rem !important;
      
      h6 {
        font-size: 0.9rem;
      }
      
      .small {
        font-size: 0.8rem;
      }
    }
  }
}

// Responsive Grid Adjustments
@media (max-width: $tablet) {
  // Stack charts on tablet
  .row > [class*="col-lg-"] {
    margin-bottom: 1rem;
  }
  
  // Full width buttons on mobile
  .btn-block-mobile {
    width: 100%;
    margin-bottom: 0.5rem;
  }
}

// Touch-friendly interactions
@media (hover: none) and (pointer: coarse) {
  // Larger touch targets
  .btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
  
  // Better spacing for touch
  .dropdown-item {
    padding: 0.75rem 1rem;
  }
  
  // Larger clickable areas
  .stats-card,
  .chart-card,
  .progress-card {
    cursor: pointer;
    
    &:active {
      transform: scale(0.98);
    }
  }
}

// Landscape mobile adjustments
@media (max-width: $tablet) and (orientation: landscape) {
  .dashboard-header {
    position: sticky;
    top: 0;
    z-index: 100;
    background: var(--cui-body-bg);
    padding-bottom: 1rem;
  }
  
  .stats-row {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }
}

// Print styles
@media print {
  .dashboard-filters,
  .btn-toolbar,
  .refresh-controls {
    display: none !important;
  }
  
  .card {
    break-inside: avoid;
    margin-bottom: 1rem;
  }
  
  .chart-card {
    height: auto !important;
  }
}

// Utility classes for responsive dashboard
.hide-mobile {
  @media (max-width: $mobile) {
    display: none !important;
  }
}

.show-mobile {
  display: none !important;
  
  @media (max-width: $mobile) {
    display: block !important;
  }
}

.text-truncate-mobile {
  @media (max-width: $mobile) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}