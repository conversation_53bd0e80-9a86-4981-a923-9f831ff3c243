{"version": 1, "dependencies": {"net8.0": {"Microsoft.AspNetCore.Hosting.Abstractions": {"type": "Direct", "requested": "[2.2.0, )", "resolved": "2.2.0", "contentHash": "ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.Http": {"type": "Direct", "requested": "[2.2.2, )", "resolved": "2.2.2", "contentHash": "BAibpoItxI5puk7YJbIGj95arZueM8B8M5xT1fXBn3hb3L2G3ucrZcYXv1gXdaroLbntUs8qeV8iuBrpjQsrKw==", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}}, "Microsoft.AspNetCore.Http.Abstractions": {"type": "Direct", "requested": "[2.2.0, )", "resolved": "2.2.0", "contentHash": "Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"type": "Direct", "requested": "[8.0.0, )", "resolved": "8.0.0", "contentHash": "ua2LSZY/f0BkNUUVPPm83eq4Xnt+FZYutiMimRrzSmv2K2t2Ia/PuP4CfibYNSwnKl6fbZ49Bwn2mQGWnmmvOA==", "dependencies": {"Microsoft.EntityFrameworkCore.Relational": "8.0.0", "Microsoft.Extensions.Identity.Stores": "8.0.0"}}, "Microsoft.EntityFrameworkCore": {"type": "Direct", "requested": "[9.0.1, )", "resolved": "9.0.1", "contentHash": "E25w4XugXNykTr5Y/sLDGaQ4lf67n9aXVPvsdGsIZjtuLmbvb9AoYP8D50CDejY8Ro4D9GK2kNHz5lWHqSK+wg==", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.1", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.1", "Microsoft.Extensions.Caching.Memory": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1"}}, "Microsoft.EntityFrameworkCore.Design": {"type": "Direct", "requested": "[9.0.1, )", "resolved": "9.0.1", "contentHash": "/pchcadGU57ChRYH0/bvLTeU/n1mpWO+0pVK7pUzzuwRu5SIQb8dVMZVPhzvEI2VO5rP1yricSQBBnOmDqQhvg==", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Build.Framework": "17.8.3", "Microsoft.Build.Locator": "1.7.8", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.MSBuild": "4.8.0", "Microsoft.EntityFrameworkCore.Relational": "9.0.1", "Microsoft.Extensions.Caching.Memory": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.DependencyModel": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Mono.TextTemplating": "3.0.0", "System.Text.Json": "9.0.1"}}, "Microsoft.Extensions.Caching.StackExchangeRedis": {"type": "Direct", "requested": "[9.0.5, )", "resolved": "9.0.5", "contentHash": "bSn4ak8sjP8dfT4OoKXcUIMwodxfQhE+6eBP9HgyNFpsffSp/Tlnjn8aaB0Sqp+5l1s7XzpKiXHHbuCiiLDnTw==", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "StackExchange.Redis": "2.7.27"}}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"type": "Direct", "requested": "[9.0.5, )", "resolved": "9.0.5", "contentHash": "cjnRtsEAzU73aN6W7vkWy8Phj5t3Xm78HSqgrbh/O4Q9SK/yN73wZVa21QQY6amSLQRQ/M8N+koGnY6PuvKQsw=="}, "Microsoft.Extensions.Hosting.Abstractions": {"type": "Direct", "requested": "[9.0.5, )", "resolved": "9.0.5", "contentHash": "3GA/dxqkP6yFe18qYRgtKYuN2onC8NfhlpNN21jptkVKk7olqBTkdT49oL0pSEz2SptRsux7LocCU7+alGnEag==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.5", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5"}}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"type": "Direct", "requested": "[9.0.4, )", "resolved": "9.0.4", "contentHash": "mw5vcY2IEc7L+IeGrxpp/J5OSnCcjkjAgJYCm/eD52wpZze8zsSifdqV7zXslSMmfJG2iIUGZyo3KuDtEFKwMQ==", "dependencies": {"Microsoft.EntityFrameworkCore": "[9.0.1, 10.0.0)", "Microsoft.EntityFrameworkCore.Relational": "[9.0.1, 10.0.0)", "Npgsql": "9.0.3"}}, "Npgsql.EntityFrameworkCore.PostgreSQL.NodaTime": {"type": "Direct", "requested": "[9.0.4, )", "resolved": "9.0.4", "contentHash": "QZ80CL3c9xzC83eVMWYWa1RcFZA6HJtpMAKFURlmz+1p0OyysSe8R6f/4sI9vk/nwqF6Fkw3lDgku/xH6HcJYg==", "dependencies": {"Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "Npgsql.NodaTime": "9.0.3"}}, "System.IdentityModel.Tokens.Jwt": {"type": "Direct", "requested": "[8.12.0, )", "resolved": "8.12.0", "contentHash": "JpkST6AQlxrXXQ05jVNqoPsU9fjIfERJdCWMxIBWzGhuNH4q/TkP5suPdlNFtHhIN+ngWQ8rmaCNY35EhACHwg==", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.12.0", "Microsoft.IdentityModel.Tokens": "8.12.0"}}, "System.Text.Encodings.Web": {"type": "Direct", "requested": "[9.0.5, )", "resolved": "9.0.5", "contentHash": "HJPmqP2FsE+WVUUlTsZ4IFRSyzw40yz0ubiTnsaqm+Xo5fFZhVRvx6Zn8tLXj92/6pbre6OA4QL2A2vnCSKxJA=="}, "AutoMapper": {"type": "Transitive", "resolved": "12.0.1", "contentHash": "hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "dependencies": {"Microsoft.CSharp": "4.7.0"}}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"type": "Transitive", "resolved": "12.0.1", "contentHash": "+g/K+Vpe3gGMKGzjslMOdqNlkikScDjWfVvmWTayrDHaG/n2pPmFBMa+jKX1r/h6BDGFdkyRjAuhFE3ykW+r1g==", "dependencies": {"AutoMapper": "[12.0.1]", "Microsoft.Extensions.Options": "6.0.0"}}, "FluentValidation": {"type": "Transitive", "resolved": "12.0.0", "contentHash": "8NVLxtMUXynRHJIX3Hn1ACovaqZIJASufXIIFkD0EUbcd5PmMsL1xUD5h548gCezJ5BzlITaR9CAMrGe29aWpA=="}, "FluentValidation.DependencyInjectionExtensions": {"type": "Transitive", "resolved": "12.0.0", "contentHash": "B28fBRL1UjhGsBC8fwV6YBZosh+SiU1FxdD7l7p5dGPgRlVI7UnM+Lgzmg+unZtV1Zxzpaw96UY2MYfMaAd8cg==", "dependencies": {"FluentValidation": "12.0.0", "Microsoft.Extensions.Dependencyinjection.Abstractions": "2.1.0"}}, "Humanizer.Core": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw=="}, "MediatR": {"type": "Transitive", "resolved": "12.5.0", "contentHash": "vqm2H8/nqL5NAJHPhsG1JOPwfkmbVrPyh4svdoRzu+uZh6Ex7PRoHBGsLYC0/RWCEJFqD1ohHNpteQvql9OktA==", "dependencies": {"MediatR.Contracts": "[2.0.1, 3.0.0)", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "MediatR.Contracts": {"type": "Transitive", "resolved": "2.0.1", "contentHash": "FYv95bNT4UwcNA+G/J1oX5OpRiSUxteXaUt2BJbRSdRNiIUNbggJF69wy6mnk2wYToaanpdXZdCwVylt96MpwQ=="}, "Microsoft.AspNetCore.Cryptography.Internal": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "buuMMCTxFcVkOkEftb2OafYxrveNGre9KJF4Oi1DkR4rxIj6oLam7Wq3g0Fp9hNVpJteKEPiupsxYnPrD/oUGA=="}, "Microsoft.AspNetCore.Cryptography.KeyDerivation": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "65w93R5wqUUs35R9wjHHDf75GqAbxJsNByKZo5TbQOWSXcUbLWrDUWBQHv78iXIT0PL1pXNqKQz7OHiHMvo0/A==", "dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "8.0.0"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.Http.Features": {"type": "Transitive", "resolved": "5.0.17", "contentHash": "3jG2xS+dx8DDCGV/F+STdPTg89lX3ao3dF/VEPvJaz3wzBIjuadipTtYNEXDIVuOPZwb6jdmhrX9jkzOIBm5cw==", "dependencies": {"Microsoft.Extensions.Primitives": "5.0.1", "System.IO.Pipelines": "5.0.2"}}, "Microsoft.AspNetCore.WebUtilities": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.Bcl.AsyncInterfaces": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg=="}, "Microsoft.Build.Framework": {"type": "Transitive", "resolved": "17.8.3", "contentHash": "NrQZJW8TlKVPx72yltGb8SVz3P5mNRk9fNiD/ao8jRSk48WqIIdCn99q4IjlVmPcruuQ+yLdjNQLL8Rb4c916g=="}, "Microsoft.Build.Locator": {"type": "Transitive", "resolved": "1.7.8", "contentHash": "sPy10x527Ph16S2u0yGME4S6ohBKJ69WfjeGG/bvELYeZVmJdKjxgnlL8cJJJLGV/cZIRqSfB12UDB8ICakOog=="}, "Microsoft.CodeAnalysis.Analyzers": {"type": "Transitive", "resolved": "3.3.4", "contentHash": "AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g=="}, "Microsoft.CodeAnalysis.Common": {"type": "Transitive", "resolved": "4.8.0", "contentHash": "/jR+e/9aT+BApoQJABlVCKnnggGQbvGh7BKq2/wI1LamxC+LbzhcLj4Vj7gXCofl1n4E521YfF9w0WcASGg/KA==", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "System.Collections.Immutable": "7.0.0", "System.Reflection.Metadata": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "Microsoft.CodeAnalysis.CSharp": {"type": "Transitive", "resolved": "4.8.0", "contentHash": "+3+qfdb/aaGD8PZRCrsdobbzGs1m9u119SkkJt8e/mk3xLJz/udLtS2T6nY27OTXxBBw10HzAbC8Z9w08VyP/g==", "dependencies": {"Microsoft.CodeAnalysis.Common": "[4.8.0]"}}, "Microsoft.CodeAnalysis.CSharp.Workspaces": {"type": "Transitive", "resolved": "4.8.0", "contentHash": "3amm4tq4Lo8/BGvg9p3BJh3S9nKq2wqCXfS7138i69TUpo/bD+XvD0hNurpEBtcNZhi1FyutiomKJqVF39ugYA==", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "[4.8.0]", "Microsoft.CodeAnalysis.Common": "[4.8.0]", "Microsoft.CodeAnalysis.Workspaces.Common": "[4.8.0]"}}, "Microsoft.CodeAnalysis.Workspaces.Common": {"type": "Transitive", "resolved": "4.8.0", "contentHash": "LXyV+MJKsKRu3FGJA3OmSk40OUIa/dQCFLOnm5X8MNcujx7hzGu8o+zjXlb/cy5xUdZK2UKYb9YaQ2E8m9QehQ==", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.CodeAnalysis.Common": "[4.8.0]", "System.Composition": "7.0.0", "System.IO.Pipelines": "7.0.0", "System.Threading.Channels": "7.0.0"}}, "Microsoft.CodeAnalysis.Workspaces.MSBuild": {"type": "Transitive", "resolved": "4.8.0", "contentHash": "IEYreI82QZKklp54yPHxZNG9EKSK6nHEkeuf+0Asie9llgS1gp0V1hw7ODG+QyoB7MuAnNQHmeV1Per/ECpv6A==", "dependencies": {"Microsoft.Build.Framework": "16.10.0", "Microsoft.CodeAnalysis.Common": "[4.8.0]", "Microsoft.CodeAnalysis.Workspaces.Common": "[4.8.0]", "System.Text.Json": "7.0.3"}}, "Microsoft.CSharp": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA=="}, "Microsoft.EntityFrameworkCore.Abstractions": {"type": "Transitive", "resolved": "9.0.1", "contentHash": "qy+taGVLUs82zeWfc32hgGL8Z02ZqAneYvqZiiXbxF4g4PBUcPRuxHM9K20USmpeJbn4/fz40GkCbyyCy5ojOA=="}, "Microsoft.EntityFrameworkCore.Analyzers": {"type": "Transitive", "resolved": "9.0.1", "contentHash": "c6ZZJZhPKrXFkE2z/81PmuT69HBL6Y68Cl0xJ5SRrDjJyq5Aabkq15yCqPg9RQ3R0aFLVaJok2DA8R3TKpejDQ=="}, "Microsoft.EntityFrameworkCore.Relational": {"type": "Transitive", "resolved": "9.0.1", "contentHash": "7Iu0h4oevRvH4IwPzmxuIJGYRt55TapoREGlluk75KCO7lenN0+QnzCl6cQDY48uDoxAUpJbpK2xW7o8Ix69dw==", "dependencies": {"Microsoft.EntityFrameworkCore": "9.0.1", "Microsoft.Extensions.Caching.Memory": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1"}}, "Microsoft.Extensions.Caching.Abstractions": {"type": "Transitive", "resolved": "9.0.5", "contentHash": "RV6wOTvH5BeVRs6cvxFuaV1ut05Dklpvq19XRO1JxAayfLWYIEP7K94aamY0iSUhoehWk1X5H6gMcbZkHuBjew==", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}}, "Microsoft.Extensions.Caching.Memory": {"type": "Transitive", "resolved": "9.0.5", "contentHash": "qDmoAzIUBup5KZG1Abv51ifbHMCWFnaXbt05l+Sd92mLOpF9OwHOuoxu3XhzXaPGfq0Ns3pv1df5l8zuKjFgGw==", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}}, "Microsoft.Extensions.Configuration.Abstractions": {"type": "Transitive", "resolved": "9.0.5", "contentHash": "ew0G6gIznnyAkbIa67wXspkDFcVektjN3xaDAfBDIPbWph+rbuGaaohFxUSGw28ht7wdcWtTtElKnzfkcDDbOQ==", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}}, "Microsoft.Extensions.DependencyInjection": {"type": "Transitive", "resolved": "9.0.1", "contentHash": "qZI42ASAe3hr2zMSA6UjM92pO1LeDq5DcwkgSowXXPY8I56M76pEKrnmsKKbxagAf39AJxkH2DY4sb72ixyOrg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1"}}, "Microsoft.Extensions.DependencyModel": {"type": "Transitive", "resolved": "9.0.1", "contentHash": "FHPy9cbb0y09riEpsrU5XYpOgf4nTfHj7a0m1wLC5DosGtjJn9g03gGg1GTJmEdRFBQrJwbwTnHqLCdNLsoYgA==", "dependencies": {"System.Text.Encodings.Web": "9.0.1", "System.Text.Json": "9.0.1"}}, "Microsoft.Extensions.Diagnostics.Abstractions": {"type": "Transitive", "resolved": "9.0.5", "contentHash": "6YfTcULCYREMTqtk+s3UiszsFV2xN2FXtxdQpurmQJY9Cp/QGiM4MTKfJKUo7AzdLuzjOKKMWjQITmvtK7AsUg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "System.Diagnostics.DiagnosticSource": "9.0.5"}}, "Microsoft.Extensions.FileProviders.Abstractions": {"type": "Transitive", "resolved": "9.0.5", "contentHash": "LLm+e8lvD+jOI+blHRSxPqywPaohOTNcVzQv548R1UpkEiNB2D+zf3RrqxBdB1LDPicRMTnfiaKJovxF8oX1bQ==", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}}, "Microsoft.Extensions.Identity.Core": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "hnXHyIQc+uc2uNMcIbr43+oNBAPEhMpW6lE8ux3MOegRz50WBna4AItlZDY7Y+Id1LLBbf73osUqeTw7CQ371w==", "dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Identity.Stores": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "DmDCpSpngZDBm44wVmxCeYs4HGJr/m32jMItp6pfb7KKtqWYw2vybHRg880j18k/eSFyM4v9uONsnEPgDdi9lg==", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Identity.Core": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}}, "Microsoft.Extensions.Logging": {"type": "Transitive", "resolved": "9.0.1", "contentHash": "E/k5r7S44DOW+08xQPnNbO8DKAQHhkspDboTThNJ6Z3/QBb4LC6gStNWzVmy3IvW7sUD+iJKf4fj0xEkqE7vnQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1"}}, "Microsoft.Extensions.Logging.Abstractions": {"type": "Transitive", "resolved": "9.0.5", "contentHash": "pP1PADCrIxMYJXxFmTVbAgEU7GVpjK5i0/tyfU9DiE0oXQy3JWQaOVgCkrCiePLgS8b5sghM3Fau3EeHiVWbCg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "System.Diagnostics.DiagnosticSource": "9.0.5"}}, "Microsoft.Extensions.ObjectPool": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g=="}, "Microsoft.Extensions.Options": {"type": "Transitive", "resolved": "9.0.5", "contentHash": "vPdJQU8YLOUSSK8NL0RmwcXJr2E0w8xH559PGQl4JYsglgilZr9LZnqV2zdgk+XR05+kuvhBEZKoDVd46o7NqA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}}, "Microsoft.Extensions.Primitives": {"type": "Transitive", "resolved": "9.0.5", "contentHash": "b4OAv1qE1C9aM+ShWJu3rlo/WjDwa/I30aIPXqDWSKXTtKl1Wwh6BZn+glH5HndGVVn3C6ZAPQj5nv7/7HJNBQ=="}, "Microsoft.IdentityModel.Abstractions": {"type": "Transitive", "resolved": "8.12.0", "contentHash": "V7fHMFpfzvx7twWMV3jrf3OFVmYn3QhUYtvfMRD9yUPs9gxnQSaRMZh5NzCsnW3ZZ80J09EE7yyjM71y9JC6hQ=="}, "Microsoft.IdentityModel.JsonWebTokens": {"type": "Transitive", "resolved": "8.12.0", "contentHash": "gOup2SmdwaXooLR0POKfrkyrw+R+G8nc8Nk80TL0196kFQK7Bg7Qr4x3jvOCm3TR8QLqU8cVpSVqBLtUaosPEg==", "dependencies": {"Microsoft.IdentityModel.Tokens": "8.12.0"}}, "Microsoft.IdentityModel.Logging": {"type": "Transitive", "resolved": "8.12.0", "contentHash": "IakwNWUTy5RlcDcKwtL5TyfDLZmA8FFnSDhfr+wfGGPMON9GkpkUY8NakIJjdy6mv6C1lM/Jkn3IuaeS9MXesA==", "dependencies": {"Microsoft.IdentityModel.Abstractions": "8.12.0"}}, "Microsoft.IdentityModel.Tokens": {"type": "Transitive", "resolved": "8.12.0", "contentHash": "WCU3wv5sioX5hhp3oHw8sCdygnfZJtRKJGCg+AckP6nbp0QGKK3VohTrxIF0dpuziLAPg57CPfS9X8UERroKxA==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.IdentityModel.Logging": "8.12.0"}}, "Microsoft.Net.Http.Headers": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0", "System.Buffers": "4.5.0"}}, "Mono.TextTemplating": {"type": "Transitive", "resolved": "3.0.0", "contentHash": "YqueG52R/Xej4VVbKuRIodjiAhV0HR/XVbLbNrJhCZnzjnSjgMJ/dCdV0akQQxavX6hp/LC6rqLGLcXeQYU7XA==", "dependencies": {"System.CodeDom": "6.0.0"}}, "NodaTime": {"type": "Transitive", "resolved": "3.2.0", "contentHash": "yoRA3jEJn8NM0/rQm78zuDNPA3DonNSZdsorMUj+dltc1D+/Lc5h9YXGqbEEZozMGr37lAoYkcSM/KjTVqD0ow=="}, "Npgsql": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "tPvY61CxOAWxNsKLEBg+oR646X4Bc8UmyQ/tJszL/7mEmIXQnnBhVJZrZEEUv0Bstu0mEsHZD5At3EO8zQRAYw==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.2"}}, "Npgsql.NodaTime": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "PMWXCft/iw+5A7eCeMcy6YZXBst6oeisbCkv2JMQVG4SAFa5vQaf6K2voXzUJCqzwOFcCWs+oT42w2uMDFpchw==", "dependencies": {"NodaTime": "3.2.0", "Npgsql": "9.0.3"}}, "Pipelines.Sockets.Unofficial": {"type": "Transitive", "resolved": "2.2.8", "contentHash": "zG2FApP5zxSx6OcdJQLbZDk2AVlN2BNQD6MorwIfV6gVj0RRxWPEp2LXAxqDGZqeNV1Zp0BNPcNaey/GXmTdvQ==", "dependencies": {"System.IO.Pipelines": "5.0.1"}}, "StackExchange.Redis": {"type": "Transitive", "resolved": "2.7.27", "contentHash": "Uqc2OQHglqj9/FfGQ6RkKFkZfHySfZlfmbCl+hc+u2I/IqunfelQ7QJi7ZhvAJxUtu80pildVX6NPLdDaUffOw==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Pipelines.Sockets.Unofficial": "2.2.8"}}, "System.Buffers": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A=="}, "System.CodeDom": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA=="}, "System.Collections.Immutable": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "dQPcs0U1IKnBdRDBkrCTi1FoajSTBzLcVTpjO4MBCMC7f4pDOIPzgBoX8JjG7X6uZRJ8EBxsi8+DR1JuwjnzOQ=="}, "System.Composition": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "tRwgcAkDd85O8Aq6zHDANzQaq380cek9lbMg5Qma46u5BZXq/G+XvIYmu+UI+BIIZ9zssXLYrkTykEqxxvhcmg==", "dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Convention": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0", "System.Composition.TypedParts": "7.0.0"}}, "System.Composition.AttributedModel": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "2QzClqjElKxgI1jK1Jztnq44/8DmSuTSGGahXqQ4TdEV0h9s2KikQZIgcEqVzR7OuWDFPGLHIprBJGQEPr8fAQ=="}, "System.Composition.Convention": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "IMhTlpCs4HmlD8B+J8/kWfwX7vrBBOs6xyjSTzBlYSs7W4OET4tlkR/Sg9NG8jkdJH9Mymq0qGdYS1VPqRTBnQ==", "dependencies": {"System.Composition.AttributedModel": "7.0.0"}}, "System.Composition.Hosting": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "eB6gwN9S+54jCTBJ5bpwMOVerKeUfGGTYCzz3QgDr1P55Gg/Wb27ShfPIhLMjmZ3MoAKu8uUSv6fcCdYJTN7Bg==", "dependencies": {"System.Composition.Runtime": "7.0.0"}}, "System.Composition.Runtime": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "aZJ1Zr5Txe925rbo4742XifEyW0MIni1eiUebmcrP3HwLXZ3IbXUj4MFMUH/RmnJOAQiS401leg/2Sz1MkApDw=="}, "System.Composition.TypedParts": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "ZK0KNPfbtxVceTwh+oHNGUOYV2WNOHReX2AXipuvkURC7s/jPwoWfsu3SnDBDgofqbiWr96geofdQ2erm/KTHg==", "dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0"}}, "System.Diagnostics.DiagnosticSource": {"type": "Transitive", "resolved": "9.0.5", "contentHash": "WoI5or8kY2VxFdDmsaRZ5yaYvvb+4MCyy66eXo79Cy1uMa7qXeGIlYmZx7R9Zy5S4xZjmqvkk2V8L6/vDwAAEA=="}, "System.IO.Pipelines": {"type": "Transitive", "resolved": "9.0.1", "contentHash": "uXf5o8eV/gtzDQY4lGROLFMWQvcViKcF8o4Q6KpIOjloAQXrnscQSu6gTxYJMHuNJnh7szIF9AzkaEq+zDLoEg=="}, "System.Reflection.Metadata": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "MclTG61lsD9sYdpNz9xsKBzjsmsfCtcMZYXz/IUr2zlhaTaABonlr1ESeompTgM+Xk+IwtGYU7/voh3YWB/fWw==", "dependencies": {"System.Collections.Immutable": "7.0.0"}}, "System.Runtime.CompilerServices.Unsafe": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg=="}, "System.Text.Json": {"type": "Transitive", "resolved": "9.0.1", "contentHash": "eqWHDZqYPv1PvuvoIIx5pF74plL3iEOZOl/0kQP+Y0TEbtgNnM2W6k8h8EPYs+LTJZsXuWa92n5W5sHTWvE3VA==", "dependencies": {"System.IO.Pipelines": "9.0.1", "System.Text.Encodings.Web": "9.0.1"}}, "System.Threading.Channels": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "qmeeYNROMsONF6ndEZcIQ+VxR4Q/TX/7uIVLJqtwIWL7dDWeh0l1UIqgo4wYyjG//5lUNhwkLDSFl+pAWO6oiA=="}, "harmoni360.application": {"type": "Project", "dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": "[12.0.1, )", "FluentValidation.DependencyInjectionExtensions": "[12.0.0, )", "Harmoni360.Domain": "[1.0.0, )", "MediatR": "[12.5.0, )", "Microsoft.AspNetCore.Http.Features": "[5.0.17, )", "Microsoft.EntityFrameworkCore": "[9.0.1, )", "Microsoft.Extensions.Caching.Memory": "[9.0.5, )", "Microsoft.Extensions.Logging.Abstractions": "[9.0.5, )"}}, "harmoni360.domain": {"type": "Project", "dependencies": {"MediatR.Contracts": "[2.0.1, )"}}}}}