name: Pull Request Validation

on:
  pull_request:
    branches: [ main, develop ]
    types: [opened, synchronize, reopened]

env:
  DOTNET_VERSION: '8.0.x'
  NODE_VERSION: '20.x'

jobs:
  # Code Quality and Linting
  code-quality:
    name: Code Quality Check
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch full history for better analysis

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: ${{ env.DOTNET_VERSION }}
          # Disable caching due to packages.lock.json being in subdirectories

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: src/Harmoni360.Web/ClientApp/package-lock.json

      - name: Restore .NET dependencies
        run: dotnet restore

      - name: Install Node.js dependencies
        working-directory: src/Harmoni360.Web/ClientApp
        run: npm ci

      - name: Run .NET code formatting check
        run: dotnet format --verify-no-changes --verbosity diagnostic

      - name: Run ESLint
        working-directory: src/Harmoni360.Web/ClientApp
        run: npm run lint

      - name: Run Prettier check
        working-directory: src/Harmoni360.Web/ClientApp
        run: npm run format:check

      - name: Run TypeScript type checking
        working-directory: src/Harmoni360.Web/ClientApp
        run: npm run type-check

  # Build and Test Validation
  build-test:
    name: Build and Test Validation
    runs-on: ubuntu-latest
    
    services:
      ********:
        image: ********:15-alpine
        env:
          POSTGRES_PASSWORD: ********
          POSTGRES_DB: harmoni360_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: ${{ env.DOTNET_VERSION }}
          # Disable caching due to packages.lock.json being in subdirectories

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: src/Harmoni360.Web/ClientApp/package-lock.json

      - name: Restore .NET dependencies
        run: dotnet restore

      - name: Install Node.js dependencies
        working-directory: src/Harmoni360.Web/ClientApp
        run: npm ci

      - name: Build .NET application
        run: dotnet build --no-restore --configuration Release

      - name: Build React application
        working-directory: src/Harmoni360.Web/ClientApp
        run: npm run build

      - name: Run .NET unit tests
        run: |
          dotnet test --no-build --configuration Release \
            --logger trx --results-directory TestResults \
            --collect:"XPlat Code Coverage" \
            /p:CollectCoverage=true \
            /p:CoverletOutputFormat=cobertura \
            /p:Threshold=80
        env:
          ConnectionStrings__DefaultConnection: "Host=localhost;Port=5432;Database=harmoni360_test;Username=********;Password=********"
          ConnectionStrings__Redis: "localhost:6379"
          Jwt__Key: "TestJwtKeyThatIsAtLeast32CharactersLongForTesting"

      - name: Run Frontend tests
        working-directory: src/Harmoni360.Web/ClientApp
        run: npm test -- --coverage --watchAll=false --coverageThreshold='{"global":{"branches":80,"functions":80,"lines":80,"statements":80}}'

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: pr-test-results-${{ github.event.number }}
          path: |
            TestResults/
            src/Harmoni360.Web/ClientApp/coverage/

  # Security Validation
  security-check:
    name: Security Validation
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Setup Node.js for security scan
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: src/Harmoni360.Web/ClientApp/package-lock.json

      - name: Install Node.js dependencies
        working-directory: src/Harmoni360.Web/ClientApp
        run: npm ci

      - name: Run npm audit
        working-directory: src/Harmoni360.Web/ClientApp
        run: npm audit --audit-level moderate
        continue-on-error: true  # Allow to fail due to permission issues in CI

      - name: Check for secrets
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: ${{ github.event.repository.default_branch }}
          head: HEAD
          extra_args: --debug --only-verified

  # Docker Build Validation
  docker-validation:
    name: Docker Build Validation
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build Docker image (no push)
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./Dockerfile.flyio
          push: false
          tags: harmoni360:pr-${{ github.event.number }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Database Migration Validation
  migration-check:
    name: Database Migration Check
    runs-on: ubuntu-latest
    
    services:
      ********:
        image: ********:15-alpine
        env:
          POSTGRES_PASSWORD: ********
          POSTGRES_DB: harmoni360_migration_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: ${{ env.DOTNET_VERSION }}
          # Disable caching due to packages.lock.json being in subdirectories

      - name: Restore dependencies
        run: dotnet restore

      - name: Install EF Core tools
        run: dotnet tool install --global dotnet-ef

      - name: Check for pending migrations
        run: |
          dotnet ef migrations list --project src/Harmoni360.Infrastructure --startup-project src/Harmoni360.Web
        env:
          ConnectionStrings__DefaultConnection: "Host=localhost;Port=5432;Database=harmoni360_migration_test;Username=********;Password=********"

      - name: Apply migrations
        run: |
          dotnet ef database update --project src/Harmoni360.Infrastructure --startup-project src/Harmoni360.Web
        env:
          ConnectionStrings__DefaultConnection: "Host=localhost;Port=5432;Database=harmoni360_migration_test;Username=********;Password=********"

  # PR Summary Comment
  pr-summary:
    name: PR Summary
    runs-on: ubuntu-latest
    needs: [code-quality, build-test, security-check, docker-validation, migration-check]
    if: always()
    
    permissions:
      issues: write
      pull-requests: write
    
    steps:
      - name: Create PR Summary Comment
        uses: actions/github-script@v7
        with:
          script: |
            const { data: comments } = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
            });
            
            const botComment = comments.find(comment => 
              comment.user.type === 'Bot' && comment.body.includes('## PR Validation Summary')
            );
            
            const jobs = [
              { name: 'Code Quality', status: '${{ needs.code-quality.result }}' },
              { name: 'Build & Test', status: '${{ needs.build-test.result }}' },
              { name: 'Security Check', status: '${{ needs.security-check.result }}' },
              { name: 'Docker Validation', status: '${{ needs.docker-validation.result }}' },
              { name: 'Migration Check', status: '${{ needs.migration-check.result }}' }
            ];
            
            const getStatusEmoji = (status) => {
              switch(status) {
                case 'success': return '✅';
                case 'failure': return '❌';
                case 'cancelled': return '⏹️';
                case 'skipped': return '⏭️';
                default: return '⏳';
              }
            };
            
            const summary = `## PR Validation Summary
            
            | Check | Status |
            |-------|--------|
            ${jobs.map(job => `| ${job.name} | ${getStatusEmoji(job.status)} ${job.status} |`).join('\n')}
            
            **Overall Status:** ${jobs.every(job => job.status === 'success') ? '✅ All checks passed' : '❌ Some checks failed'}
            
            ---
            *This comment is automatically updated on each push.*`;
            
            if (botComment) {
              await github.rest.issues.updateComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: botComment.id,
                body: summary
              });
            } else {
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.issue.number,
                body: summary
              });
            }
