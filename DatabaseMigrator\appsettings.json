{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=Harmoni360_Dev;Username=postgres;Password=***********", "Redis": "localhost:6379"}, "Jwt": {"Key": "YourSecretKeyHereWhichShouldBeAtLeast32CharactersLongForSecurity", "Issuer": "Harmoni360", "Audience": "Harmoni360Users", "ExpirationMinutes": "60", "RefreshTokenExpirationDays": "7"}, "Seq": {"ServerUrl": "http://localhost:5341"}, "DataSeeding": {"SeedIncidents": true, "ReSeedIncidents": false, "ReSeedUsers": false, "SeedPPEData": true, "ReSeedPPEData": false, "SeedPPEItems": true, "ReSeedPPEItems": false, "SeedHazards": true, "ReSeedHazards": false, "SeedHealthData": true, "ReSeedHealthData": false, "SeedExtendedData": true, "ReSeedExtendedData": false, "ReSeedModulePermissions": false, "ReSeedRoleModulePermissions": false}}