name: Harmoni360 CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  DOTNET_VERSION: '8.0.x'
  NODE_VERSION: '20.x'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Build and Test Job
  build-and-test:
    name: Build and Test
    runs-on: ubuntu-latest
    
    services:
      ********:
        image: ********:15-alpine
        env:
          POSTGRES_PASSWORD: ********
          POSTGRES_DB: harmoni360_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: ${{ env.DOTNET_VERSION }}
          cache: true

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: src/Harmoni360.Web/ClientApp/package-lock.json

      - name: Restore .NET dependencies
        run: dotnet restore

      - name: Install Node.js dependencies
        working-directory: src/Harmoni360.Web/ClientApp
        run: npm ci

      - name: Build .NET application
        run: dotnet build --no-restore --configuration Release

      - name: Build React application
        working-directory: src/Harmoni360.Web/ClientApp
        run: npm run build

      - name: Run .NET tests
        run: |
          dotnet test --no-build --configuration Release \
            --logger trx --results-directory TestResults \
            --collect:"XPlat Code Coverage" \
            /p:CollectCoverage=true \
            /p:CoverletOutputFormat=cobertura
        env:
          ConnectionStrings__DefaultConnection: "Host=localhost;Port=5432;Database=harmoni360_test;Username=********;Password=********"
          ConnectionStrings__Redis: "localhost:6379"
          Jwt__Key: "TestJwtKeyThatIsAtLeast32CharactersLongForTesting"

      - name: Run Frontend tests
        working-directory: src/Harmoni360.Web/ClientApp
        run: npm test -- --coverage --watchAll=false

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results
          path: |
            TestResults/
            src/Harmoni360.Web/ClientApp/coverage/

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: |
            src/Harmoni360.Web/bin/Release/
            src/Harmoni360.Web/ClientApp/dist/

  # Security Scanning Job
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: build-and-test
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Setup .NET for security scan
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: ${{ env.DOTNET_VERSION }}

      - name: Setup Node.js for npm audit
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: src/Harmoni360.Web/ClientApp/package-lock.json

      - name: Install Node.js dependencies for audit
        working-directory: src/Harmoni360.Web/ClientApp
        run: npm ci

      - name: Install security scan tools
        run: |
          npm install -g audit-ci

      - name: Run npm audit
        working-directory: src/Harmoni360.Web/ClientApp
        run: |
          npm audit --audit-level moderate || true
          audit-ci --config audit-ci.json || true
        continue-on-error: true

  # Docker Build Job
  docker-build:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: [build-and-test, security-scan]
    if: github.event_name != 'pull_request'
    
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./Dockerfile.flyio
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: docker-build
    if: github.ref == 'refs/heads/develop' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    environment: staging
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Fly CLI
        uses: superfly/flyctl-actions/setup-flyctl@master

      - name: Deploy to Fly.io Staging
        run: |
          flyctl deploy --config fly.staging.toml
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

      - name: Run database migrations
        run: |
          flyctl ssh console --config fly.staging.toml -C "cd /app && dotnet ef database update"
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

      - name: Health check
        run: |
          sleep 30
          curl -f https://harmoni360-staging.fly.dev/health || exit 1

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: docker-build
    if: github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Fly CLI
        uses: superfly/flyctl-actions/setup-flyctl@master

      - name: Deploy to Fly.io Production
        run: |
          flyctl deploy --config fly.toml
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

      - name: Run database migrations
        run: |
          flyctl ssh console --config fly.toml -C "cd /app && dotnet ef database update"
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

      - name: Health check
        run: |
          sleep 30
          curl -f https://harmoni360-app.fly.dev/health || exit 1

      - name: Notify deployment success
        uses: 8398a7/action-slack@v3
        if: success()
        continue-on-error: true
        with:
          status: success
          text: "🚀 Harmoni360 successfully deployed to production!"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify deployment failure
        uses: 8398a7/action-slack@v3
        if: failure()
        continue-on-error: true
        with:
          status: failure
          text: "❌ Harmoni360 production deployment failed!"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
