# Prometheus Configuration for Harmoni360 Production Monitoring

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'harmoni360-prod'
    environment: 'production'

# Alertmanager configuration (optional)
# alerting:
#   alertmanagers:
#     - static_configs:
#         - targets:
#           - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  - "alert_rules.yml"

# A scrape configuration containing exactly one endpoint to scrape:
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # Harmoni360 Application Metrics
  - job_name: 'harmoni360-app'
    static_configs:
      - targets: ['app:8080']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # PostgreSQL Database Metrics (requires postgres_exporter)
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s

  # Redis Metrics (requires redis_exporter)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s

  # Nginx Metrics (requires nginx-prometheus-exporter)
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s

  # Node/System Metrics (requires node_exporter)
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s

  # Docker Container Metrics (requires cadvisor)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s

  # Custom application health checks
  - job_name: 'harmoni360-health'
    static_configs:
      - targets: ['app:8080']
    scrape_interval: 10s
    metrics_path: /health
    scrape_timeout: 5s
    honor_labels: true
