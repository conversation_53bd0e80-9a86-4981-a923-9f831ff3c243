# Harmoni360 Production Environment Configuration
# Copy this file to .env.prod and customize the values for your deployment

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================
COMPOSE_PROJECT_NAME=harmoni360-prod
BUILD_VERSION=latest

# =============================================================================
# PATHS CONFIGURATION
# =============================================================================
# Base data directory (ensure this exists and has proper permissions)
DATA_PATH=/opt/harmoni360/data
LOG_PATH=/opt/harmoni360/logs
BACKUP_PATH=/opt/harmoni360/backups

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
POSTGRES_DB=Harmoni360_Prod
POSTGRES_USER=harmoni360
# REQUIRED: Set a strong password for PostgreSQL
POSTGRES_PASSWORD=

# PostgreSQL Performance Tuning (adjust based on your server specs)
POSTGRES_SHARED_BUFFERS=2GB
POSTGRES_EFFECTIVE_CACHE_SIZE=6GB
POSTGRES_WORK_MEM=64MB
POSTGRES_MAINTENANCE_WORK_MEM=512MB
POSTGRES_MAX_CONNECTIONS=300

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
# REQUIRED: Set a strong password for Redis
REDIS_PASSWORD=

# =============================================================================
# JWT CONFIGURATION
# =============================================================================
# REQUIRED: Generate a secure JWT key (minimum 32 characters)
# Use: openssl rand -base64 32
JWT_KEY=
JWT_ISSUER=Harmoni360
JWT_AUDIENCE=Harmoni360Users
JWT_EXPIRATION_MINUTES=60
JWT_REFRESH_EXPIRATION_DAYS=7

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Maximum file upload size in bytes (100MB default)
MAX_FILE_SIZE=104857600

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# REQUIRED: Generate Seq admin password hash
# Use: echo 'your-password' | docker run --rm -i datalust/seq config hash
SEQ_ADMIN_PASSWORD_HASH=

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
# REQUIRED: Set Grafana admin password
GRAFANA_ADMIN_PASSWORD=

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================
# Domain name for SSL certificate
DOMAIN_NAME=your-domain.com
SSL_EMAIL=<EMAIL>

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
# Backup retention settings
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Allowed hosts (comma-separated)
ALLOWED_HOSTS=your-domain.com,www.your-domain.com,localhost

# CORS origins for production (comma-separated)
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
# Application performance settings
ASPNETCORE_THREADPOOL_MINTHREADS=50
ASPNETCORE_THREADPOOL_MAXTHREADS=200

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================
# Email settings for notifications (optional)
SMTP_HOST=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=Harmoni360

# =============================================================================
# EXTERNAL SERVICES (Optional)
# =============================================================================
# Cloud backup settings (optional)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_S3_BUCKET=
AWS_REGION=us-east-1

# =============================================================================
# DEVELOPMENT/DEBUG (Set to false in production)
# =============================================================================
ENABLE_SWAGGER=false
ENABLE_DETAILED_ERRORS=false
ENABLE_DEVELOPER_EXCEPTION_PAGE=false
