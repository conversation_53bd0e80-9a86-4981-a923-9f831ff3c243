-- Apply Module-Based Authorization Migration
-- This script applies the module-based authorization schema changes

-- Check if migration has already been applied
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM "__EFMigrationsHistory" 
        WHERE "MigrationId" = '20250606143400_AddModuleBasedAuthorization'
    ) THEN
        
        -- Add new columns to Roles table for module-based authorization
        ALTER TABLE "Roles" 
        ADD COLUMN "RoleType" integer NOT NULL DEFAULT 9,
        ADD COLUMN "IsActive" boolean NOT NULL DEFAULT true,
        ADD COLUMN "DisplayOrder" integer NOT NULL DEFAULT 0;

        -- Create ModulePermissions table
        CREATE TABLE "ModulePermissions" (
            "Id" integer NOT NULL GENERATED BY DEFAULT AS IDENTITY,
            "Module" integer NOT NULL,
            "Permission" integer NOT NULL,
            "Name" character varying(100) NOT NULL,
            "Description" character varying(500) NOT NULL,
            "IsActive" boolean NOT NULL DEFAULT true,
            "CreatedAt" timestamp with time zone NOT NULL,
            "CreatedBy" text NULL,
            "LastModifiedAt" timestamp with time zone NULL,
            "LastModifiedBy" text NULL,
            CONSTRAINT "PK_ModulePermissions" PRIMARY KEY ("Id")
        );

        -- Create RoleModulePermissions table
        CREATE TABLE "RoleModulePermissions" (
            "Id" integer NOT NULL GENERATED BY DEFAULT AS IDENTITY,
            "RoleId" integer NOT NULL,
            "ModulePermissionId" integer NOT NULL,
            "IsActive" boolean NOT NULL DEFAULT true,
            "GrantedAt" timestamp with time zone NOT NULL DEFAULT NOW(),
            "GrantedByUserId" integer NULL,
            "GrantReason" character varying(500) NULL,
            "CreatedAt" timestamp with time zone NOT NULL,
            "CreatedBy" text NULL,
            "LastModifiedAt" timestamp with time zone NULL,
            "LastModifiedBy" text NULL,
            CONSTRAINT "PK_RoleModulePermissions" PRIMARY KEY ("Id"),
            CONSTRAINT "FK_RoleModulePermissions_ModulePermissions_ModulePermissionId" 
                FOREIGN KEY ("ModulePermissionId") REFERENCES "ModulePermissions" ("Id") ON DELETE CASCADE,
            CONSTRAINT "FK_RoleModulePermissions_Roles_RoleId" 
                FOREIGN KEY ("RoleId") REFERENCES "Roles" ("Id") ON DELETE CASCADE,
            CONSTRAINT "FK_RoleModulePermissions_Users_GrantedByUserId" 
                FOREIGN KEY ("GrantedByUserId") REFERENCES "Users" ("Id") ON DELETE SET NULL
        );

        -- Create indexes for ModulePermissions
        CREATE UNIQUE INDEX "IX_ModulePermission_Module_Permission" 
            ON "ModulePermissions" ("Module", "Permission");
        CREATE INDEX "IX_ModulePermission_IsActive" 
            ON "ModulePermissions" ("IsActive");
        CREATE INDEX "IX_ModulePermission_Module" 
            ON "ModulePermissions" ("Module");

        -- Create indexes for RoleModulePermissions
        CREATE UNIQUE INDEX "IX_RoleModulePermission_Role_ModulePermission" 
            ON "RoleModulePermissions" ("RoleId", "ModulePermissionId");
        CREATE INDEX "IX_RoleModulePermission_IsActive" 
            ON "RoleModulePermissions" ("IsActive");
        CREATE INDEX "IX_RoleModulePermission_RoleId" 
            ON "RoleModulePermissions" ("RoleId");
        CREATE INDEX "IX_RoleModulePermission_ModulePermissionId" 
            ON "RoleModulePermissions" ("ModulePermissionId");
        CREATE INDEX "IX_RoleModulePermission_GrantedByUserId" 
            ON "RoleModulePermissions" ("GrantedByUserId");

        -- Create indexes for new Role columns
        CREATE UNIQUE INDEX "IX_Roles_RoleType" 
            ON "Roles" ("RoleType");
        CREATE INDEX "IX_Roles_IsActive" 
            ON "Roles" ("IsActive");

        -- Update existing roles with appropriate RoleType values
        UPDATE "Roles" SET "RoleType" = 1 WHERE "Name" = 'SuperAdmin';
        UPDATE "Roles" SET "RoleType" = 2 WHERE "Name" = 'Developer';
        UPDATE "Roles" SET "RoleType" = 3 WHERE "Name" IN ('Admin', 'Administrator');
        UPDATE "Roles" SET "RoleType" = 4 WHERE "Name" IN ('IncidentManager', 'Incident Manager');
        UPDATE "Roles" SET "RoleType" = 5 WHERE "Name" IN ('RiskManager', 'Risk Manager');
        UPDATE "Roles" SET "RoleType" = 6 WHERE "Name" IN ('PPEManager', 'PPE Manager');
        UPDATE "Roles" SET "RoleType" = 7 WHERE "Name" IN ('HealthMonitor', 'Health Monitor');
        UPDATE "Roles" SET "RoleType" = 8 WHERE "Name" IN ('Reporter', 'Employee');
        UPDATE "Roles" SET "RoleType" = 9 WHERE "Name" IN ('Viewer', 'Guest');

        -- Add migration record
        INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
        VALUES ('20250606143400_AddModuleBasedAuthorization', '8.0.0');

        RAISE NOTICE 'Module-based authorization migration applied successfully.';
    ELSE
        RAISE NOTICE 'Module-based authorization migration already applied.';
    END IF;
END $$;