{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "", "Redis": ""}, "Jwt": {"Key": "", "Issuer": "Harmoni360", "Audience": "Harmoni360Users", "ExpirationMinutes": "60", "RefreshTokenExpirationDays": "7"}, "Seq": {"ServerUrl": ""}, "Application": {"DemoMode": false, "Environment": "Production", "DemoSettings": {"ShowDemoBanner": false, "AllowDataModification": true, "AllowUserCreation": true, "AllowDataDeletion": true, "ShowSampleDataLabels": false}}, "DataSeeding": {"ForceReseed": false, "Categories": {"Essential": true, "SampleData": false, "UserAccounts": false}}}