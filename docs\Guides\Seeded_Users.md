# 🔑 Harmoni360 - Seeded User Credentials

> **Quick Reference**: Default login credentials for testing and development

## Demo User Credentials
The application includes seeded demo users for immediate testing:

| Role | Email | Password | Permissions |
|------|-------|----------|-------------|
| **Super Admin** | <EMAIL> | SuperAdmin123! | Full system access including configuration |
| **Developer** | <EMAIL> | Developer123! | System configuration and module management |
| **Admin** | <EMAIL> | Admin123! | Full system access |
| **Incident Manager** | <EMAIL> | IncidentMgr123! | Incident management specialist |
| **Risk Manager** | <EMAIL> | RiskMgr123! | Risk assessment specialist |
| **PPE Manager** | <EMAIL> | PPEMgr123! | PPE management specialist |
| **Health Monitor** | <EMAIL> | HealthMon123! | Health monitoring specialist |
| **Reporter** | <EMAIL> | Reporter123! | Read-only access to reporting |
| **Viewer** | <EMAIL> | Viewer123! | Read-only access to dashboards |
| **Reporter** | <EMAIL> | Employee123! | Basic reporting access |
| **Viewer** | <EMAIL> | Employee123! | Basic viewing access |

## 🚀 Quick Login Examples

### Super Admin Access
```
Email: <EMAIL>
Password: SuperAdmin123!
```

### Developer Access
```
Email: <EMAIL>
Password: Developer123!
```

### Admin Access
```
Email: <EMAIL>
Password: Admin123!
```

### Incident Manager Access  
```
Email: <EMAIL>
Password: IncidentMgr123!
```

### Regular Employee Access
```
Email: <EMAIL>
Password: Employee123!
```

## 🔗 Useful Links

### Local Development
- **React Frontend**: http://localhost:5173
- **API Backend**: http://localhost:5000
- **API Swagger**: http://localhost:5000/swagger

### Docker Deployment
- **Full Application**: http://localhost:8080
- **API Swagger**: http://localhost:8080/swagger

### Guides
- **Getting Started**: [Getting_Started_Guide.md](./Getting_Started_Guide.md)
- **Full Authentication Documentation**: [Authentication_Guide.md](./Authentication_Guide.md)

## ⚠️ Important Notes

- **Development Only**: These are demo credentials for development/testing
- **Auto-Fill Available**: The login page displays demo users with auto-fill buttons
- **JWT Tokens**: Valid for 60 minutes (configurable)
- **Role-Based Access**: Currently all authenticated users can access the dashboard
- **Future Enhancement**: Role-specific restrictions will be added for individual modules

---
*For complete setup instructions and troubleshooting, see the [Getting Started Guide](./Getting_Started_Guide.md)*