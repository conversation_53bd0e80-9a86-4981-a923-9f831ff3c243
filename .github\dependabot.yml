version: 2
updates:
  # .NET dependencies
  - package-ecosystem: "nuget"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "America/Los_Angeles"
    open-pull-requests-limit: 10
    reviewers:
      - "risky-biz"
    assignees:
      - "risky-biz"
    commit-message:
      prefix: "chore"
      prefix-development: "chore"
      include: "scope"
    labels:
      - "dependencies"
      - "dotnet"
    ignore:
      # Ignore major version updates for stable packages
      - dependency-name: "Microsoft.EntityFrameworkCore*"
        update-types: ["version-update:semver-major"]
      - dependency-name: "Microsoft.AspNetCore*"
        update-types: ["version-update:semver-major"]

  # Node.js dependencies for React frontend
  - package-ecosystem: "npm"
    directory: "/src/Harmoni360.Web/ClientApp"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "America/Los_Angeles"
    open-pull-requests-limit: 10
    reviewers:
      - "risky-biz"
    assignees:
      - "risky-biz"
    commit-message:
      prefix: "chore"
      prefix-development: "chore"
      include: "scope"
    labels:
      - "dependencies"
      - "frontend"
      - "npm"
    ignore:
      # Ignore major version updates for React and related packages
      - dependency-name: "react"
        update-types: ["version-update:semver-major"]
      - dependency-name: "react-dom"
        update-types: ["version-update:semver-major"]
      - dependency-name: "@types/react"
        update-types: ["version-update:semver-major"]
      - dependency-name: "@types/react-dom"
        update-types: ["version-update:semver-major"]
    groups:
      # Group React-related updates
      react:
        patterns:
          - "react*"
          - "@types/react*"
      # Group testing-related updates
      testing:
        patterns:
          - "@testing-library/*"
          - "jest*"
          - "vitest*"
      # Group build tools
      build-tools:
        patterns:
          - "vite*"
          - "@vitejs/*"
          - "typescript"
          - "eslint*"
          - "prettier*"

  # Docker dependencies
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "tuesday"
      time: "09:00"
      timezone: "America/Los_Angeles"
    open-pull-requests-limit: 5
    reviewers:
      - "risky-biz"
    assignees:
      - "risky-biz"
    commit-message:
      prefix: "chore"
      include: "scope"
    labels:
      - "dependencies"
      - "docker"

  # GitHub Actions dependencies
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "wednesday"
      time: "09:00"
      timezone: "America/Los_Angeles"
    open-pull-requests-limit: 5
    reviewers:
      - "risky-biz"
    assignees:
      - "risky-biz"
    commit-message:
      prefix: "chore"
      include: "scope"
    labels:
      - "dependencies"
      - "github-actions"
    groups:
      # Group GitHub Actions updates
      github-actions:
        patterns:
          - "actions/*"
          - "github/*"
